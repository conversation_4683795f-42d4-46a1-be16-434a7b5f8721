{% extends "base.html" %}

{% block title %}设备智能分析{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">设备智能分析</h1>

    <!-- 设备基本信息卡片 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">设备基本信息</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <p><strong>设备ID：</strong>{{ device.device_id }}</p>
                </div>
                <div class="col-md-3">
                    <p><strong>状态：</strong>
                        {% if device.status == 'online' %}
                        <span class="badge badge-success">在线</span>
                        {% else %}
                        <span class="badge badge-danger">离线</span>
                        {% endif %}
                    </p>
                </div>
                <div class="col-md-3">
                    <p><strong>固件版本：</strong>{{ device.firmware_version }}</p>
                </div>
                <div class="col-md-3">
                    <p><strong>位置：</strong>{{ device_data.location }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 设备健康分析 -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">设备健康分析</h6>
                </div>
                <div class="card-body">
                    <div id="healthAnalysis">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 故障预测 -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">故障预测</h6>
                </div>
                <div class="card-body">
                    <div id="failurePrediction">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 固件升级建议 -->
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">固件升级建议</h6>
                </div>
                <div class="card-body">
                    <div id="upgradeRecommendation">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 历史任务记录 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">历史任务记录</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="historyTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>状态</th>
                            <th>事件</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for task in task_history %}
                        <tr>
                            <td>{{ task.timestamp }}</td>
                            <td>{{ task.status }}</td>
                            <td>{{ task.event }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 加载设备健康分析
    function loadHealthAnalysis() {
        $.ajax({
            url: "{{ url_for('ai.analyze_device') }}",
            type: "POST",
            contentType: "application/json",
            data: JSON.stringify({
                device_id: "{{ device.id }}"
            }),
            success: function(response) {
                if (response.success) {
                    $("#healthAnalysis").html(`<pre class="text-wrap">${response.analysis}</pre>`);
                } else {
                    $("#healthAnalysis").html(`<div class="alert alert-danger">${response.message}</div>`);
                }
            },
            error: function(xhr, status, error) {
                $("#healthAnalysis").html(`<div class="alert alert-danger">加载失败: ${error}</div>`);
            }
        });
    }

    // 加载故障预测
    function loadFailurePrediction() {
        $.ajax({
            url: "{{ url_for('ai.predict_failure') }}",
            type: "POST",
            contentType: "application/json",
            data: JSON.stringify({
                device_id: "{{ device.id }}"
            }),
            success: function(response) {
                if (response.success) {
                    $("#failurePrediction").html(`<pre class="text-wrap">${response.prediction}</pre>`);
                } else {
                    $("#failurePrediction").html(`<div class="alert alert-danger">${response.message}</div>`);
                }
            },
            error: function(xhr, status, error) {
                $("#failurePrediction").html(`<div class="alert alert-danger">加载失败: ${error}</div>`);
            }
        });
    }

    // 加载升级建议
    function loadUpgradeRecommendation() {
        $.ajax({
            url: "{{ url_for('ai.recommend_upgrade') }}",
            type: "POST",
            contentType: "application/json",
            data: JSON.stringify({
                device_id: "{{ device.id }}"
            }),
            success: function(response) {
                if (response.success) {
                    $("#upgradeRecommendation").html(`<pre class="text-wrap">${response.recommendation}</pre>`);
                } else {
                    $("#upgradeRecommendation").html(`<div class="alert alert-danger">${response.message}</div>`);
                }
            },
            error: function(xhr, status, error) {
                $("#upgradeRecommendation").html(`<div class="alert alert-danger">加载失败: ${error}</div>`);
            }
        });
    }

    $(document).ready(function() {
        // 初始化DataTables
        $('#historyTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.21/i18n/Chinese.json"
            },
            "order": [[0, "desc"]]
        });

        // 加载AI分析数据
        loadHealthAnalysis();
        loadFailurePrediction();
        loadUpgradeRecommendation();
    });
</script>
{% endblock %} 