{% extends "base.html" %}

{% block title %}首页 - OTA设备管理系统{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="border-bottom pb-2"><i class="fas fa-tachometer-alt"></i> 系统概览</h2>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card h-100 border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-microchip"></i> 设备总数</h5>
                </div>
                <div class="card-body text-center">
                    <h2 class="display-4 text-primary">{{ devices|length }}</h2>
                    <p class="text-muted">已注册设备</p>
                </div>
                <div class="card-footer bg-transparent">
                    <a href="{{ url_for('device.devices') }}" class="btn btn-primary w-100">管理设备</a>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-3">
            <div class="card h-100 border-success">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-check-circle"></i> 在线设备</h5>
                </div>
                <div class="card-body text-center">
                    <h2 class="display-4 text-success" id="onlineDevicesCount">{{ devices|selectattr('is_online',
                        'equalto', true)|list|length }}</h2>
                    <p class="text-muted">当前在线</p>
                </div>
                <div class="card-footer bg-transparent">
                    <a href="{{ url_for('device.devices') }}" class="btn btn-success w-100">查看设备</a>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-3">
            <div class="card h-100 border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0"><i class="fas fa-exclamation-triangle"></i> 离线设备</h5>
                </div>
                <div class="card-body text-center">
                    <h2 class="display-4 text-warning" id="offlineDevicesCount">{{ devices|selectattr('is_online',
                        'equalto', false)|list|length }}</h2>
                    <p class="text-muted">当前离线</p>
                </div>
                <div class="card-footer bg-transparent">
                    <button class="btn btn-warning w-100 batch-ota-btn">批量升级</button>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-3">
            <div class="card h-100 border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-tools"></i> 实用工具</h5>
                </div>
                <div class="card-body text-center">
                    <i class="fas fa-code display-4 text-info"></i>
                    <p class="text-muted mt-3">协议解析工具</p>
                </div>
                <div class="card-footer bg-transparent">
                    <a href="{{ url_for('static', filename='tools/message-parser/index.html') }}"
                        class="btn btn-info w-100" target="_blank">
                        <i class="fas fa-external-link-alt"></i> 打开工具
                    </a>
                </div>
            </div>
        </div>

        <!-- <div class="col-md-3 mb-3">
            <div class="card h-100 border-secondary">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-toolbox"></i> 工具箱</h5>
                </div>
                <div class="card-body text-center">
                    <i class="fas fa-toolbox display-4 text-secondary"></i>
                    <p class="text-muted mt-3">集成多种实用工具</p>
                </div>
                <div class="card-footer bg-transparent">
                    <a href="{{ url_for('tools.toolbox') }}" class="btn btn-secondary w-100">
                        <i class="fas fa-external-link-alt"></i> 打开工具箱
                    </a>
                </div>
            </div>
        </div> -->
    </div>
</div>

{% include 'components/ota_modal.html' %}
{% include 'components/batch_ota_modal.html' %}

{% endblock %}

{% block scripts %}
<script>
    // 搜索和筛选功能
    function filterDevices() {
        var searchText = document.getElementById('searchInput').value.toLowerCase();
        var statusFilter = document.getElementById('statusFilter').value;
        var productKeyFilter = document.getElementById('productKeyFilter').value.toLowerCase();
        var firmwareFilter = document.getElementById('firmwareFilter').value.toLowerCase();
        var remarkFilter = document.getElementById('remarkFilter').value.toLowerCase();
        var rows = document.querySelectorAll('tbody tr');

        rows.forEach(function (row) {
            var deviceId = row.cells[1].textContent.toLowerCase();  // 设备ID在第2列
            var deviceRemark = row.cells[2].textContent.toLowerCase();  // 设备备注在第3列
            var deviceStatus = row.cells[3].textContent.trim();  // 在线状态在第4列
            var productKey = row.cells[4].textContent.toLowerCase();  // 产品密钥在第5列
            var firmwareVersion = row.cells[5].textContent.toLowerCase();  // 固件版本在第6列

            var matchesSearch = deviceId.includes(searchText);
            var matchesStatus = statusFilter === 'all' ||
                (statusFilter === 'online' && deviceStatus.includes('在线')) ||
                (statusFilter === 'offline' && deviceStatus.includes('离线'));
            var matchesProductKey = productKey.includes(productKeyFilter);
            var matchesFirmware = firmwareVersion.includes(firmwareFilter);
            var matchesRemark = deviceRemark.includes(remarkFilter);

            if (matchesSearch && matchesStatus && matchesProductKey && matchesFirmware && matchesRemark) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    // 添加事件监听器
    document.getElementById('searchInput').addEventListener('keyup', filterDevices);
    document.getElementById('statusFilter').addEventListener('change', filterDevices);
    document.getElementById('productKeyFilter').addEventListener('keyup', filterDevices);
    document.getElementById('firmwareFilter').addEventListener('keyup', filterDevices);
    document.getElementById('remarkFilter').addEventListener('keyup', filterDevices);
    document.addEventListener('DOMContentLoaded', function () {
        // 添加Animate.css到页面
        if (!document.querySelector('link[href*="animate.css"]')) {
            const animateCss = document.createElement('link');
            animateCss.rel = 'stylesheet';
            animateCss.href = 'https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css';
            document.head.appendChild(animateCss);
        }

        // 添加自定义样式
        const style = document.createElement('style');
        style.textContent = `
        .modal-backdrop {
            transition: opacity 0.3s ease-in-out;
        }
        .modal.fade .modal-dialog {
            transition: transform 0.3s ease-out;
        }
        .list-group-item {
            transition: all 0.2s ease;
        }
        .list-group-item:hover {
            transform: translateX(5px);
            background-color: rgba(13, 110, 253, 0.05);
        }
    `;
        document.head.appendChild(style);

        // 初始化模态框元素
        const batchOtaModal = document.getElementById('batchOtaModal');

        // 为批量升级按钮添加点击事件
        const batchOtaButtons = document.querySelectorAll('.batch-ota-btn');
        batchOtaButtons.forEach(button => {
            button.addEventListener('click', function (e) {
                e.preventDefault();

                // 检查是否有选中的设备
                const checkboxes = document.querySelectorAll('tbody .device-checkbox:checked');
                if (checkboxes.length === 0) {
                    alert('请先选择要升级的设备');
                    return;
                }

                showBatchOtaModal();
            });
        });

        // 为关闭按钮添加事件
        document.getElementById('batchOtaModalClose').addEventListener('click', closeBatchOtaModal);
        document.getElementById('cancelBatchOta').addEventListener('click', closeBatchOtaModal);

        // 处理全选复选框
        const selectAllCheckbox = document.getElementById('selectAll');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function () {
                const checkboxes = document.querySelectorAll('tbody tr:not([style*="display: none"]) .device-checkbox');
                Array.from(checkboxes).forEach(checkbox => checkbox.checked = this.checked);
            });
        }

        // 当单个复选框改变时，更新全选框状态
        document.querySelectorAll('.device-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const visibleCheckboxes = document.querySelectorAll('tbody tr:not([style*="display: none"]) .device-checkbox');
                const checkedVisibleCheckboxes = document.querySelectorAll('tbody tr:not([style*="display: none"]) .device-checkbox:checked');
                document.getElementById('selectAll').checked = visibleCheckboxes.length === checkedVisibleCheckboxes.length;
            });
        });

        // 处理批量OTA表单提交
        const batchOtaForm = document.getElementById('batchOtaForm');
        if (batchOtaForm) {
            batchOtaForm.addEventListener('submit', async function (e) {
                e.preventDefault();
                const formData = new FormData(this);
                const selectedDevices = Array.from(document.querySelectorAll('tbody .device-checkbox:checked')).map(cb => cb.value);
                const firmwareId = formData.get('firmware_id');
                
                if (!firmwareId) {
                    alert('请选择要升级的固件');
                    return;
                }
                
                try {
                    const response = await fetch('/ota/start', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            device_ids: selectedDevices,
                            firmware_id: firmwareId
                        })
                    });
                    const data = await response.json();

                    if (data.success) {
                        closeBatchOtaModal();
                        alert('OTA升级任务已创建');
                        location.reload();
                    } else {
                        alert(data.message || '创建OTA任务失败');
                    }
                } catch (error) {
                    console.error('Error:', error);
                    alert('创建OTA任务失败，请重试');
                }
            });
        }

        // 添加固件选择事件监听
        const firmwareSelect = document.getElementById('firmwareSelect');
        if (firmwareSelect) {
            firmwareSelect.addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                const version = selectedOption.dataset.version;
                const description = selectedOption.dataset.description;
                
                // 可以在这里添加显示固件详情的逻辑
                if (version && description) {
                    console.log(`已选择固件版本: ${version}`);
                    console.log(`固件描述: ${description}`);
                }
            });
        }

        // 确保页面加载时没有任何模态框背景残留
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());
        document.body.classList.remove('modal-open');

        // 监听模态框事件，防止多次触发
        document.getElementById('batchOtaModal').addEventListener('hidden.bs.modal', function (e) {
            // 确保只在正常关闭时执行，动画关闭由closeBatchOtaModal处理
            if (!e.target.classList.contains('animating')) {
                document.body.classList.remove('modal-open');
                const backdrops = document.querySelectorAll('.modal-backdrop');
                backdrops.forEach(backdrop => backdrop.remove());
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';
            }
        });
    });
</script>
{% endblock %}