{% extends "base.html" %}
<!-- stylelint-disable -->

{% block title %}OTA任务 - OTA设备管理系统{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="border-bottom pb-2"><i class="fas fa-sync-alt"></i> OTA任务</h2>
    </div>
</div>

<div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="card-title mb-0"><i class="fas fa-list"></i> 任务列表</h5>
                        </div>
                        <div class="col-auto">
                            <div class="row g-3">
                                <div class="col-auto">
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                        <input type="date" class="form-control" id="dateFilter">
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-filter"></i></span>
                                        <select class="form-select" id="statusFilter">
                                            <option value="">全部状态</option>
                                            <option value="成功">成功</option>
                                            <option value="失败">失败</option>
                                            <option value="等待中">等待中</option>
                                            <option value="进行中">进行中</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                        <input type="text" class="form-control" id="deviceIdFilter" placeholder="设备ID">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>任务ID</th>
                                    <th>设备ID</th>
                                <th>固件版本</th>
                                <th>状态</th>
                                <th>进度</th>
                                <th>创建时间</th>
                                <th>更新时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for task in tasks %}
                                <tr data-task-id="{{ task.id }}">
                                <td>{{ task.id }}</td>
                                    <td>{{ task.device.device_id }}</td>
                                    <td>
                                        <span class="badge bg-primary">v{{ task.firmware_version }}</span>
                                    </td>
                                <td>
                                    {% if task.status == '成功' %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check-circle me-1"></i>成功
                                        </span>
                                    {% elif task.status == '失败' %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times-circle me-1"></i>失败
                                        </span>
                                        <!-- 等待中 -->
                                    {% elif task.status == '等待中' %}
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-hourglass-half me-1"></i>等待中
                                        </span>
                                    {% else %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-sync fa-spin me-1"></i>进行中
                                        </span>
                                    {% endif %}
                                </td>
                                    <td style="width: 200px;">
                                        <div class="progress">
                                            <div class="progress-bar progress-bar-striped"
                                                role="progressbar"
                                                data-progress="{{ task.progress }}"
                                                aria-valuenow="{{ task.progress|default(0) }}"
                                                aria-valuemin="0"
                                                aria-valuemax="100">
                                            {{ task.progress }}%
                                        </div>
                                    </div>
                                </td>
                                <td>{{ task.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                <td>{{ task.updated_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                <td>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-info" onclick="viewTaskDetails('{{ task.id }}')" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            {% if task.status == '失败' %}
                                            <button type="button" class="btn btn-sm btn-warning" onclick="retryTask('{{ task.id }}')" title="重试">
                                                <i class="fas fa-redo"></i>
                                    </button>
                                            {% endif %}
                                            <a href="{{ url_for('main.delete_task', id=task.id) }}" class="btn btn-sm btn-danger" onclick="return confirm('确定要删除此任务吗？')" title="删除">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 任务详情模态框 -->
<div class="modal fade" id="taskDetailsModal" tabindex="-1" aria-labelledby="taskDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="taskDetailsModalLabel">
                    <i class="fas fa-info-circle"></i> 任务详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>任务ID:</strong> <span id="detailTaskId"></span></p>
                        <p><strong>设备ID:</strong> <span id="detailDeviceId"></span></p>
                        <p><strong>固件版本:</strong> <span id="detailFirmwareVersion"></span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>状态:</strong> <span id="detailStatus"></span></p>
                        <p><strong>进度:</strong> <span id="detailProgress"></span>%</p>
                        <p><strong>创建时间:</strong> <span id="detailCreatedAt"></span></p>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <p><strong>错误信息:</strong></p>
                        <pre id="detailError" class="bg-light p-2" style="max-height: 200px; overflow-y: auto;"></pre>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
<script>
    // 打印调试日志到控制台
    console.log("OTA任务脚本加载中...");
    
    // 全局变量定义
    let taskDetailsModal = null;
    let socket = null;
    let reconnectAttempts = 0;
    const maxReconnectAttempts = 5;
    
    // 筛选功能实现
    function filterTasks() {
        const dateFilter = document.getElementById('dateFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;
        const deviceIdFilter = document.getElementById('deviceIdFilter').value.toLowerCase();
        
        const rows = document.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            const taskDate = row.querySelector('td:nth-child(6)').textContent.split(' ')[0];
            const taskStatus = row.querySelector('td:nth-child(4) .badge').textContent.trim();
            const taskDeviceId = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
            
            const matchesDate = !dateFilter || taskDate === dateFilter;
            const matchesStatus = !statusFilter || taskStatus === statusFilter;
            const matchesDeviceId = !deviceIdFilter || taskDeviceId.includes(deviceIdFilter);
            
            if (matchesDate && matchesStatus && matchesDeviceId) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    // 添加筛选事件监听器
    document.getElementById('dateFilter').addEventListener('change', filterTasks);
    document.getElementById('statusFilter').addEventListener('change', filterTasks);
    document.getElementById('deviceIdFilter').addEventListener('keyup', filterTasks);
    
    // WebSocket连接初始化函数
    function initializeWebSocket() {
        try {
            console.log("初始化WebSocket连接...");
            
            // 创建Socket.IO连接
            socket = io(window.location.origin, {
                reconnection: true,
                reconnectionDelay: 1000,
                reconnectionDelayMax: 5000,
                reconnectionAttempts: maxReconnectAttempts
            });
            

            // 连接成功事件
            socket.on('connect', function() {
                console.log('WebSocket连接成功，Socket ID:', socket.id);
                reconnectAttempts = 0;
                console.log('注册的事件监听器:', socket._callbacks);
            });
            
            // 连接错误事件
            socket.on('connect_error', function(error) {
                console.error('WebSocket连接错误:', error);
                reconnectAttempts++;
                
                if (reconnectAttempts >= maxReconnectAttempts) {
                    console.error('WebSocket重连次数超过限制，请刷新页面重试');
                }
            });

            socket.on('disconnect', function(reason) {
                console.log('WebSocket断开连接，原因:', reason);
            });
            
            // 监听任务状态更新
            socket.on('ota_task_update', function(data) {
                console.log("收到任务状态更新:", data);
                if (data && data.task_id) {
                    updateTaskStatus(data);
                } else {
                    console.error("收到的数据格式不正确:", data);
                }
            });

            socket.onAny((eventName, ...args) => {
                console.log('收到事件:', eventName, args);
            });
        } catch (error) {
            console.error('初始化WebSocket失败:', error);
        }
    }
    
    // 更新任务状态的函数
    function updateTaskStatus(data) {
        const taskRow = document.querySelector(`tr[data-task-id="${data.task_id}"]`);
        if (taskRow) {
            console.log("找到任务行，更新状态");
            // 更新状态
            const statusCell = taskRow.querySelector('td:nth-child(4)');
            const progressBar = taskRow.querySelector('.progress-bar');
            
            // 更新状态标签
            let statusHtml = '';
            if (data.status === '成功') {
                statusHtml = '<span class="badge bg-success"><i class="fas fa-check-circle me-1"></i>成功</span>';
                progressBar.classList.remove('progress-bar-animated');
                progressBar.classList.add('bg-success');
                
                console.log("任务成功");
            } else if (data.status === '失败') {
                statusHtml = '<span class="badge bg-danger"><i class="fas fa-times-circle me-1"></i>失败</span>';
                progressBar.classList.remove('progress-bar-animated');
                progressBar.classList.add('bg-danger');
                
                console.log("任务失败，添加重试按钮");
                // 为失败的任务添加重试按钮
                const actionCell = taskRow.querySelector('td:nth-child(8)');
                if (actionCell && !actionCell.querySelector('.btn-warning')) {
                    const btnGroup = actionCell.querySelector('.btn-group');
                    if (btnGroup) {
                        const retryBtn = document.createElement('button');
                        retryBtn.type = 'button';
                        retryBtn.className = 'btn btn-sm btn-warning';
                        retryBtn.title = '重试';
                        retryBtn.innerHTML = '<i class="fas fa-redo"></i>';
                        retryBtn.onclick = function() { retryTask(data.task_id); };
                        
                        // 插入到第二个位置
                        if (btnGroup.children.length > 1) {
                            btnGroup.insertBefore(retryBtn, btnGroup.children[1]);
                        } else {
                            btnGroup.appendChild(retryBtn);
                        }
                    }
                }
            } else if (data.status === '等待中') {
                statusHtml = '<span class="badge bg-secondary"><i class="fas fa-hourglass-half me-1"></i>等待中</span>';
                progressBar.classList.remove('progress-bar-animated');
                progressBar.classList.add('bg-secondary');
            } else {
                // 如果已经添加了动画效果，则不添加
                statusHtml = '<span class="badge bg-warning"><i class="fas fa-sync fa-spin me-1"></i>进行中</span>';
                progressBar.classList.add('progress-bar-animated', 'progress-bar-striped');
                // if (!progressBar.classList.contains('progress-bar-animated')) {
                // }
                console.log("任务进行中");
            }
            statusCell.innerHTML = statusHtml;
            
            // 更新进度条 - 添加平滑过渡效果
            const currentProgress = parseInt(progressBar.getAttribute('aria-valuenow') || 0);
            const targetProgress = data.progress;
            
            // 如果进度有变化，添加过渡效果
            if (currentProgress !== targetProgress) {
                // 添加过渡效果
                progressBar.style.transition = 'width 0.5s ease-in-out';
                
                // 设置新的进度值
                progressBar.style.width = `${targetProgress}%`;
                progressBar.setAttribute('aria-valuenow', targetProgress);
                progressBar.textContent = `${targetProgress}%`;
                
                // 记录进度变化
                console.log(`进度从 ${currentProgress}% 更新到 ${targetProgress}%`);
            }
            
            // 如果任务完成，2秒后刷新页面
            if (data.status === '成功' || data.status === '失败') {
                console.log("任务完成，2秒后刷新页面");
                setTimeout(() => location.reload(), 2000);
            }
        } else {
            console.log("未找到任务行");
        }
    }
    
    // 确保DOM加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        console.log("DOM已加载完成，开始初始化...");
        
        // 初始化模态框
        const taskDetailsModalEl = document.getElementById('taskDetailsModal');
        if (taskDetailsModalEl) {
            taskDetailsModal = new bootstrap.Modal(taskDetailsModalEl);
            console.log("模态框初始化成功");
        } else {
            console.error("无法找到模态框元素");
        }
        
        // 初始化WebSocket连接
        initializeWebSocket();
        
        // 初始化进度条
        document.querySelectorAll('.progress-bar[data-progress]').forEach(function(el) {
            var progress = el.getAttribute('data-progress');
            el.style.width = progress + '%';
            
            // 如果任务正在进行中，添加动画效果
            if (el.closest('tr').querySelector('.badge.bg-warning')) {
                el.classList.add('progress-bar-animated', 'progress-bar-striped');
            }
        });
    });

    // 检查是否需要重定向到登录页面
    function checkLoginRedirect(response) {
        // 如果返回了HTML而不是JSON，那么可能是登录页面
        if (response.headers.get('Content-Type').includes('text/html')) {
            console.log("收到HTML响应，可能是登录页面");
            alert("您的登录已过期，将跳转到登录页面");
            window.location.href = '/login';
            return true;
        }
        return false;
    }

    // 查看任务详情
    window.viewTaskDetails = function(taskId) {
        console.log("查看任务详情被点击，任务ID:", taskId);
        
        // 如果模态框还没初始化，则初始化它
        if (!taskDetailsModal) {
            const taskDetailsModalEl = document.getElementById('taskDetailsModal');
            if (!taskDetailsModalEl) {
                console.error("无法找到任务详情模态框元素");
                return;
            }
            taskDetailsModal = new bootstrap.Modal(taskDetailsModalEl);
        }
        
        // 显示加载状态
        document.getElementById('detailTaskId').textContent = '加载中...';
        document.getElementById('detailDeviceId').textContent = '加载中...';
        document.getElementById('detailFirmwareVersion').textContent = '加载中...';
        document.getElementById('detailStatus').textContent = '加载中...';
        document.getElementById('detailProgress').textContent = '0';
        document.getElementById('detailCreatedAt').textContent = '加载中...';
        document.getElementById('detailError').textContent = '加载中...';
        
        // 显示模态框
        taskDetailsModal.show();
        
        // 构建正确的URL
        const url = `/ota/task/${taskId}`;
        console.log("正在发送请求到:", url);
        
        // 获取任务详情数据
        fetch(url)
            .then(response => {
                console.log("收到响应:", response.status, response.headers.get('Content-Type'));
                
                // 检查是否需要重定向到登录页面
                if (checkLoginRedirect(response)) {
                    return Promise.reject(new Error('需要登录'));
                }
                
                if (!response.ok) {
                    throw new Error(`网络响应错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("获取到的任务数据:", data);
                document.getElementById('detailTaskId').textContent = data.id;
                document.getElementById('detailDeviceId').textContent = data.device_name;
                document.getElementById('detailFirmwareVersion').textContent = data.firmware_version;
                document.getElementById('detailStatus').textContent = data.status;
                document.getElementById('detailProgress').textContent = data.progress;
                document.getElementById('detailCreatedAt').textContent = data.created_at;
                document.getElementById('detailError').textContent = data.error_message || '无';
            })
            .catch(error => {
                console.error('获取任务详情失败:', error);
                if (error.message !== '需要登录') {
                    alert('获取任务详情失败: ' + error.message);
                    // 关闭模态框
                    taskDetailsModal.hide();
                }
            });
    };
    
    // 重试任务
    window.retryTask = function(taskId) {
        console.log("重试任务被点击，任务ID:", taskId);
        
        if (confirm('确定要重试此任务吗？')) {
            // 构建正确的URL
            const url = `/ota/task/${taskId}/retry`;
            console.log("正在发送POST请求到:", url);
            
            // 设置请求头
            const headers = {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            };
            
            fetch(url, {
                method: 'POST',
                headers: headers,
                credentials: 'same-origin'
            })
            .then(response => {
                console.log("收到响应:", response.status, response.headers.get('Content-Type'));
                
                // 检查是否需要重定向到登录页面
                if (checkLoginRedirect(response)) {
                    return Promise.reject(new Error('需要登录'));
                }
                
                if (!response.ok) {
                    throw new Error(`网络响应错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("重试任务响应:", data);
                if (data.success) {
                    alert('任务已重新开始');
                    location.reload();
                } else {
                    alert(data.message || '重试失败');
                }
            })
            .catch(error => {
                console.error('重试任务失败:', error);
                if (error.message !== '需要登录') {
                    alert('重试任务失败: ' + error.message);
                }
            });
        }
    };
    
    console.log("OTA任务脚本加载完成");
</script>
{% endblock %} 