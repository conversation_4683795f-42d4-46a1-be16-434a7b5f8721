请用中文与我交流。

项目背景：
这是一个使用Python+Flask开发的充电桩管理系统的后端项目。当前我正在查看设备管理页面相关的代码，包括：
- 前端模板：ota_tasks.html（OTA任务 - OTA设备管理系统）
- 后端路由：routes/ota_task.py、以及一些ota相关的py文件。
- 相关的设备处理模块

当前存在的问题：
1. **页面刷新过度问题**：许多操作完成后不必要地进行页面跳转和刷新，用户体验差。具体例子：
   - OTA任务完成后，页面会自动刷新，这是不必要的
   - 需要改为Ajax异步操作，操作完成后仅更新相关UI元素，而不是整页刷新

2. **性能问题**：OTA记录的数量大幅增加导致页面加载缓慢
   - 当前实现：一次性加载并显示所有OTA记录，导致页面打开极其缓慢
   - 修改复杂度：任何改动都需要考虑对现有功能的影响


解决方案要求：
需求：希望你能够完善的解决上面遇到的两个问题，在修改时注意相关的功能的实现也需要对应修改。
2. 实现设备列表的分页加载或懒加载机制，解决性能问题，同时保持美观，分页功能尽可能完善丰富
3. 在优化过程中，确保以下功能正常工作：
   - OTA记录查看功能
4. 每个修改步骤都要考虑对相关功能的影响，确保功能完整性

请先分析当前代码结构，然后提供详细的解决方案和实施步骤。