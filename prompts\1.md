请用中文与我交流。

项目背景：
这是一个使用Python+Flask开发的充电桩管理系统的后端项目。当前我正在查看OTA任务管理页面相关的代码，包括：
- 前端模板：templates/ota_tasks.html（OTA任务管理页面）
- 后端路由：routes/ota_task.py 以及其他OTA相关的Python文件
- 相关的OTA任务处理模块

当前存在的问题：
1. **页面刷新过度问题**：许多操作完成后不必要地进行页面跳转和刷新，用户体验差。具体例子：
   - OTA任务状态更新后，页面会自动刷新，这是不必要的
   - 需要改为Ajax异步操作，操作完成后仅更新相关UI元素（如表格行的状态），而不是整页刷新

2. **性能问题**：OTA任务记录数量大幅增加导致页面加载缓慢
   - 当前实现：一次性加载并显示所有OTA任务记录，导致页面打开极其缓慢
   - 影响范围：页面渲染时间长，用户等待时间过长
   - 修改复杂度：任何改动都需要考虑对现有功能的影响

解决方案要求：
1. **Ajax异步化改造**：将OTA任务相关的操作（状态查询、任务管理等）改为Ajax异步请求，避免不必要的页面刷新
2. **分页加载机制**：实现OTA任务列表的分页加载，解决性能问题，要求：
   - 支持服务端分页，每页显示合理数量的记录（建议20-50条）
   - 分页控件美观且功能完善（包括页码跳转、上一页/下一页、总数显示等）
   - 支持搜索和筛选功能（按设备ID、任务状态、时间范围等）
3. **功能完整性保证**：在优化过程中，确保以下功能正常工作：
   - OTA任务记录查看功能
   - OTA任务状态实时更新
   - OTA任务的创建、删除、重试等操作
   - 任务详情查看功能
4. **用户体验优化**：
   - 添加加载状态指示器
   - 实现无刷新的状态更新
   - 保持页面响应速度
5. **代码质量要求**：
   - 每个修改步骤都要考虑对相关功能的影响，确保功能完整性
   - 保持与现有设备管理页面优化方案的一致性
   - 确保代码的可维护性和扩展性

实施要求：
请先分析当前ota_tasks.html和routes/ota_task.py的代码结构，识别需要优化的具体功能点，然后提供详细的解决方案和分步骤的实施计划。特别关注表格行的data-task-id属性的使用，确保Ajax更新时能正确定位和更新对应的任务记录。