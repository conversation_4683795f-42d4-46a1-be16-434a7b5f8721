#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
设备数据库服务
提供设备信息的数据库操作功能
"""

import logging
from typing import Optional, Tuple
from flask import current_app
from models.device import Device
from models.database import db

class DeviceDatabaseService:
    """设备数据库服务"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def update_device_server_info(self, original_device_id: str, target_product_key: str, 
                                 new_device_id: Optional[str] = None) -> Tuple[bool, str]:
        """
        更新设备的服务器信息
        
        Args:
            original_device_id: 原始设备ID
            target_product_key: 目标产品密钥
            new_device_id: 新设备ID（可选）
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            # 查找设备
            device = Device.query.filter_by(device_id=original_device_id).first()
            if not device:
                return False, f"未找到设备ID为 {original_device_id} 的设备"
            
            # 更新产品密钥
            device.product_key = target_product_key
            
            # 如果提供了新设备ID且与原设备ID不同，则更新设备ID
            if new_device_id and new_device_id != original_device_id:
                # 检查新设备ID是否已存在
                existing_device = Device.query.filter_by(device_id=new_device_id).first()
                if existing_device and existing_device.id != device.id:
                    return False, f"设备ID {new_device_id} 已存在"
                
                device.device_id = new_device_id
                self.logger.info(f"设备ID从 {original_device_id} 更新为 {new_device_id}")
            
            # 提交更改
            db.session.commit()
            
            update_info = f"设备 {original_device_id} 的产品密钥已更新为 {target_product_key}"
            if new_device_id and new_device_id != original_device_id:
                update_info += f"，设备ID已更新为 {new_device_id}"
            
            self.logger.info(update_info)
            return True, update_info
            
        except Exception as e:
            db.session.rollback()
            error_msg = f"更新设备服务器信息失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def batch_update_device_server_info(self, device_ids: list, source_product_key: str, 
                                       target_product_key: str) -> Tuple[bool, str, list]:
        """
        批量更新设备的服务器信息
        
        Args:
            device_ids: 设备ID列表
            source_product_key: 源产品密钥
            target_product_key: 目标产品密钥
            
        Returns:
            Tuple[bool, str, list]: (是否有成功, 总体消息, 详细结果列表)
        """
        try:
            # 获取符合条件的设备
            devices = Device.query.filter(
                Device.id.in_(device_ids),
                Device.product_key == source_product_key
            ).all()
            
            if not devices:
                return False, f"没有找到产品密钥为 {source_product_key} 的设备", []
            
            results = []
            success_count = 0
            
            for device in devices:
                try:
                    # 更新设备的产品密钥
                    device.product_key = target_product_key
                    success_count += 1
                    
                    results.append({
                        'device_id': device.device_id,
                        'device_name': device.device_remark or device.device_id,
                        'success': True,
                        'message': '数据库更新成功'
                    })
                    
                    self.logger.info(f"设备 {device.device_id} 的产品密钥已更新为 {target_product_key}")
                    
                except Exception as e:
                    results.append({
                        'device_id': device.device_id,
                        'device_name': device.device_remark or device.device_id,
                        'success': False,
                        'message': f'数据库更新失败: {str(e)}'
                    })
                    self.logger.error(f"更新设备 {device.device_id} 失败: {e}")
            
            # 提交所有更改
            if success_count > 0:
                db.session.commit()
                message = f"批量更新完成，成功 {success_count}/{len(devices)} 个设备"
                self.logger.info(message)
                return True, message, results
            else:
                db.session.rollback()
                return False, "没有设备更新成功", results
                
        except Exception as e:
            db.session.rollback()
            error_msg = f"批量更新设备服务器信息失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, []
    
    def get_device_by_id(self, device_id: str) -> Optional[Device]:
        """根据设备ID获取设备"""
        try:
            return Device.query.filter_by(device_id=device_id).first()
        except Exception as e:
            self.logger.error(f"查询设备失败: {e}")
            return None
    
    def get_device_by_db_id(self, db_id: int) -> Optional[Device]:
        """根据数据库ID获取设备"""
        try:
            return Device.query.get(db_id)
        except Exception as e:
            self.logger.error(f"查询设备失败: {e}")
            return None

# 创建全局服务实例
device_database_service = DeviceDatabaseService()
