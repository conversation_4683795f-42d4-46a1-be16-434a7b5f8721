{% extends "base.html" %}

{% block title %}设备位置地图{% endblock %}

{% block head %}
<style>
    /* 地图容器样式 */
    #container {
        width: 100%;
        height: 650px;
        margin-bottom: 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    /* 设备信息窗体样式 */
    .device-info {
        padding: 15px;
        min-width: 250px;
        border-radius: 8px;
        background: linear-gradient(to bottom, #ffffff, #f8f9fa);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    .device-info h3 {
        margin: 0 0 12px 0;
        color: #2c3e50;
        font-size: 18px;
        font-weight: 600;
        border-bottom: 2px solid #3498db;
        padding-bottom: 8px;
    }
    .device-info p {
        margin: 8px 0;
        color: #34495e;
        font-size: 14px;
        line-height: 1.5;
    }
    .device-info .device-id {
        color: #7f8c8d;
        font-size: 13px;
    }
    .device-info .device-address {
        margin-top: 10px;
        padding-top: 10px;
        border-top: 1px dashed #e0e0e0;
    }
    
    /* 错误消息样式 */
    .error-message {
        color: #e74c3c;
        text-align: center;
        padding: 20px;
        background-color: #fdf3f2;
        border: 1px solid #fadbd8;
        border-radius: 8px;
        margin-top: 15px;
        position: relative;
        z-index: 2;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }
    
    /* 控制面板样式 */
    .map-controls {
        margin-bottom: 15px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 8px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }
    
    .map-controls .btn {
        margin-right: 10px;
        margin-bottom: 10px;
        border-radius: 20px;
        padding: 8px 16px;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .map-controls .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    /* 统计卡片样式 */
    .stats-card {
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        transition: transform 0.3s ease;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
    }
    
    .stats-card .stats-icon {
        font-size: 2.5rem;
        margin-bottom: 10px;
        color: #3498db;
    }
    
    .stats-card .stats-number {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 5px;
    }
    
    .stats-card .stats-label {
        color: #7f8c8d;
        font-size: 0.9rem;
    }
    
    /* 加载动画 */
    .loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid rgba(0, 0, 0, 0.1);
        border-radius: 50%;
        border-top-color: #3498db;
        animation: spin 1s ease-in-out infinite;
        margin-right: 10px;
        vertical-align: middle;
    }
    
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
    
    /* 响应式调整 */
    @media (max-width: 768px) {
        #container {
            height: 500px;
        }
        
        .stats-card {
            margin-bottom: 15px;
        }
    }
    
    /* 搜索输入框和按钮高度统一 */
    .input-group .form-control,
    .input-group .btn {
        height: 42px;
        box-sizing: border-box;
    }
    .input-group .form-control {
        border-radius: 20px 0 0 20px;
        border-right: none;
    }
    .input-group .btn {
        border-radius: 0 20px 20px 0;
        margin-left: -1px;
        border-left: none;
    }
    .input-group {
        box-shadow: 0 2px 8px rgba(52,152,219,0.08);
    }
</style>
<!-- 重要，请到 https://console.amap.com 申请 JS API 的 key和密钥 -->
<script> 
    window._AMapSecurityConfig = {
        securityJsCode: '290ddc4b0d33be7bc9b354bc6a4ca614',
    }
</script>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h2><i class="fas fa-map-marker-alt text-primary"></i> 设备位置地图</h2>
        <p class="text-muted">查看所有设备的地理位置分布</p>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="stats-card bg-white">
            <div class="stats-icon"><i class="fas fa-map-marked-alt text-primary"></i></div>
            <div class="stats-number" id="total-devices">--</div>
            <div class="stats-label">设备总数</div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stats-card bg-white">
            <div class="stats-icon"><i class="fas fa-check-circle text-success"></i></div>
            <div class="stats-number" id="valid-locations">--</div>
            <div class="stats-label">有效位置</div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stats-card bg-white">
            <div class="stats-icon"><i class="fas fa-exclamation-triangle text-warning"></i></div>
            <div class="stats-number" id="invalid-locations">--</div>
            <div class="stats-label">无效位置</div>
        </div>
    </div>
</div>

<!-- 地图控制面板 -->
<div class="row mb-4">
    <div class="col">
        <div class="map-controls">
            <div class="d-flex align-items-center gap-2">
                <div class="input-group" style="width: 300px;">
                    <input type="text" id="device-search" class="form-control" placeholder="输入设备ID或备注搜索...">
                    <button id="search-btn" class="btn btn-primary">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </div>
                <button id="refresh-btn" class="btn btn-primary">
                    <i class="fas fa-sync-alt"></i> 刷新位置
                </button>
                <button id="center-map-btn" class="btn btn-info">
                    <i class="fas fa-crosshairs"></i> 居中地图
                </button>
                <button id="toggle-cluster-btn" class="btn btn-secondary">
                    <i class="fas fa-layer-group"></i> 切换聚合
                </button>
                <div class="ms-auto">
                    <span id="last-update-time" class="text-muted">
                        <i class="fas fa-clock"></i> 上次更新: 未更新
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 地图卡片 -->
<div class="row">
    <div class="col">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-map"></i> 设备位置分布图</h5>
            </div>
            <div class="card-body">
                <div id="container"></div>
                <div id="error-message" class="error-message" style="display: none;"></div>
            </div>
        </div>
    </div>
</div>

<!-- 加载地图JSAPI脚本 -->
<script src="https://webapi.amap.com/maps?v=1.4.15&key=6f025e700cbacbb0bb866712d20bb35c&plugin=AMap.MarkerClusterer,AMap.Scale,AMap.ToolBar"></script>
<script>
    // 全局变量
    var map;
    var markers = [];
    var infoWindows = [];
    var markerClusterer = null;
    var isClustering = false;
    var deviceLocations = []; // 存储所有设备位置数据
    var deviceStatusCache = {}; // 存储设备状态
    
    // 初始化地图
    function initMap() {
        map = new AMap.Map('container', {
            resizeEnable: true,
            zoom: 11,
            center: [116.397428, 39.90923],
            mapStyle: 'amap://styles/normal', // 使用清新风格
            features: ['bg', 'road', 'building'],
            showBuildingBlock: true
        });
        
        // 添加地图控件
        map.addControl(new AMap.Scale());
        map.addControl(new AMap.ToolBar({
            position: 'RB'
        }));
        
        // 等待地图加载完成后再加载设备位置
        map.on('complete', function() {
            console.log('地图加载完成，开始加载设备位置');
            loadDeviceLocations();
        });
    }
    
    // 获取设备状态
    function fetchDeviceStatus() {
        return fetch('/api/device_status')
            .then(response => response.json())
            .then(data => {
                deviceStatusCache = data;
                return data;
            })
            .catch(error => {
                console.error('获取设备状态失败:', error);
                return {};
            });
    }
    
    // 修改loadDeviceLocations，先获取设备状态再加载位置
    function loadDeviceLocations() {
        document.getElementById('refresh-btn').innerHTML = '<span class="loading-spinner"></span> 加载中...';
        document.getElementById('refresh-btn').disabled = true;

        console.log('开始加载设备位置数据');
        Promise.all([
            fetch('/api/device/locations').then(r => r.json()),
            fetchDeviceStatus()
        ]).then(([locations, statusCache]) => {
            deviceLocations = locations;
            clearMarkers();

            if (!locations || locations.length === 0) {
                document.getElementById('error-message').textContent = '暂无设备位置数据';
                document.getElementById('error-message').style.display = 'block';
                updateStats(0, 0, 0);
                updateLastUpdateTime();
                resetRefreshButton();
                return;
            }

            document.getElementById('error-message').style.display = 'none';

            let validMarkersCount = 0;
            let invalidLocationsCount = 0;
            const validLocations = locations.filter(location => {
                const lat = parseFloat(location.latitude);
                const lng = parseFloat(location.longitude);
                if (isNaN(lat) || isNaN(lng) || lat === 0 || lng === 0) {
                    invalidLocationsCount++;
                    return false;
                }
                if (lat < -90 || lat > 90 || lng < -180 || lng > 180) {
                    invalidLocationsCount++;
                    return false;
                }
                return true;
            });

            updateStats(locations.length, validLocations.length, invalidLocationsCount);

            if (validLocations.length === 0) {
                document.getElementById('error-message').textContent = '没有有效的设备位置数据';
                document.getElementById('error-message').style.display = 'block';
                updateLastUpdateTime();
                resetRefreshButton();
                return;
            }

            validLocations.forEach(location => {
                const lat = parseFloat(location.latitude);
                const lng = parseFloat(location.longitude);
                // 获取设备在线状态
                let isOnline = false;
                if (deviceStatusCache && deviceStatusCache[location.device_id]) {
                    isOnline = deviceStatusCache[location.device_id].is_online;
                }
                // 根据状态选择不同颜色icon
                const iconUrl = isOnline
                    ? 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png' // 在线-蓝色
                    : 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png'; // 离线-红色
                var marker = new AMap.Marker({
                    position: [lng, lat],
                    title: location.device_name || location.device_id,
                    icon: new AMap.Icon({
                        size: new AMap.Size(32, 32),
                        image: iconUrl,
                        imageSize: new AMap.Size(32, 32)
                    })
                });
                marker.setMap(map);
                var infoWindow = new AMap.InfoWindow({
                    content: `
                        <div class="device-info">
                            <h3>${location.device_name || location.device_id}</h3>
                            <p class="device-id"><i class="fas fa-fingerprint"></i> 设备ID: ${location.device_id}</p>
                            <p><i class="fas fa-circle" style="color:${isOnline ? '#27ae60' : '#e74c3c'}"></i> 状态: ${isOnline ? '在线' : '离线'}</p>
                            <p class="device-address"><i class="fas fa-map-marker-alt"></i> 地址: ${location.address || '暂无地址信息'}</p>
                        </div>
                    `,
                    offset: new AMap.Pixel(0, -30)
                });
                marker.on('click', () => {
                    infoWindow.open(map, marker.getPosition());
                });
                markers.push(marker);
                infoWindows.push(infoWindow);
                validMarkersCount++;
            });

            if (validMarkersCount > 0) {
                map.setFitView(markers, {
                    padding: [60, 60, 60, 60]
                });
            }
            updateLastUpdateTime();
            resetRefreshButton();
        }).catch(error => {
            console.error('加载设备位置失败:', error);
            document.getElementById('error-message').textContent = '加载设备位置失败: ' + error.message;
            document.getElementById('error-message').style.display = 'block';
            resetRefreshButton();
        });
    }
    
    // 清除所有标记
    function clearMarkers() {
        markers.forEach(marker => {
            marker.setMap(null);
        });
        infoWindows.forEach(infoWindow => {
            infoWindow.close();
        });
        markers = [];
        infoWindows = [];
        
        // 清除聚合
        if (markerClusterer) {
            markerClusterer.setMap(null);
            markerClusterer = null;
        }
    }
    
    // 更新统计信息
    function updateStats(total, valid, invalid) {
        document.getElementById('total-devices').textContent = total;
        document.getElementById('valid-locations').textContent = valid;
        document.getElementById('invalid-locations').textContent = invalid;
    }
    
    // 更新最后更新时间
    function updateLastUpdateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString();
        document.getElementById('last-update-time').innerHTML = `<i class="fas fa-clock"></i> 上次更新: ${timeString}`;
    }
    
    // 重置刷新按钮
    function resetRefreshButton() {
        document.getElementById('refresh-btn').innerHTML = '<i class="fas fa-sync-alt"></i> 刷新位置';
        document.getElementById('refresh-btn').disabled = false;
    }
    
    // 切换标记聚合
    function toggleMarkerCluster() {
        if (isClustering) {
            // 关闭聚合
            if (markerClusterer) {
                markerClusterer.setMap(null);
                markerClusterer = null;
            }
            isClustering = false;
            document.getElementById('toggle-cluster-btn').innerHTML = '<i class="fas fa-layer-group"></i> 开启聚合';
        } else {
            // 开启聚合
            if (markers.length > 0) {
                markerClusterer = new AMap.MarkerClusterer(map, markers, {
                    gridSize: 80,
                    maxZoom: 16
                });
                isClustering = true;
                document.getElementById('toggle-cluster-btn').innerHTML = '<i class="fas fa-layer-group"></i> 关闭聚合';
            }
        }
    }
    
    // 居中地图
    function centerMap() {
        if (markers.length > 0) {
            map.setFitView(markers, {
                padding: [60, 60, 60, 60]
            });
        }
    }
    
    // 搜索设备
    function searchDevice() {
        const searchInput = document.getElementById('device-search').value.trim();
        if (!searchInput) {
            showError('请输入搜索内容');
            return;
        }
        
        // 在设备位置数据中查找匹配的设备
        const devices = deviceLocations.filter(loc => {
            const deviceId = (loc.device_id || '').toLowerCase();
            const deviceName = (loc.device_name || '').toLowerCase();
            const deviceNote = (loc.note || '').toLowerCase();
            const searchTerm = searchInput.toLowerCase();
            
            // 精确匹配设备ID或模糊匹配设备名称和备注
            return deviceId === searchTerm || 
                   deviceName.includes(searchTerm) || 
                   deviceNote.includes(searchTerm);
        });
        
        if (devices.length === 0) {
            showError('未找到匹配的设备');
            return;
        }
        
        // 清除现有标记
        clearMarkers();
        
        // 创建新的标记
        devices.forEach(device => {
            const lat = parseFloat(device.latitude);
            const lng = parseFloat(device.longitude);
            
            if (isNaN(lat) || isNaN(lng) || lat === 0 || lng === 0) {
                console.warn(`设备 ${device.device_id} 位置数据无效`);
                return;
            }
            
            // 创建标记
            const marker = new AMap.Marker({
                position: [lng, lat],
                title: device.device_name || device.device_id,
                icon: new AMap.Icon({
                    size: new AMap.Size(32, 32),
                    image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
                    imageSize: new AMap.Size(32, 32)
                })
            });
            
            // 设置地图
            marker.setMap(map);
            
            // 创建信息窗体
            const infoWindow = new AMap.InfoWindow({
                content: `
                    <div class="device-info">
                        <h3>${device.device_name || device.device_id}</h3>
                        <p class="device-id"><i class="fas fa-fingerprint"></i> 设备ID: ${device.device_id}</p>
                        ${device.note ? `<p><i class="fas fa-sticky-note"></i> 备注: ${device.note}</p>` : ''}
                        <p class="device-address"><i class="fas fa-map-marker-alt"></i> 地址: ${device.address || '暂无地址信息'}</p>
                    </div>
                `,
                offset: new AMap.Pixel(0, -30)
            });
            
            // 绑定点击事件
            marker.on('click', () => {
                infoWindow.open(map, marker.getPosition());
            });
            
            markers.push(marker);
            infoWindows.push(infoWindow);
        });
        
        // 如果有找到设备，调整地图视图
        if (markers.length > 0) {
            map.setFitView(markers, {
                padding: [60, 60, 60, 60]
            });
            
            // 如果只找到一个设备，显示其信息窗口
            if (markers.length === 1) {
                infoWindows[0].open(map, markers[0].getPosition());
            }
        }
    }
    
    // 显示错误信息
    function showError(message) {
        const errorElement = document.getElementById('error-message');
        errorElement.textContent = message;
        errorElement.style.display = 'block';
        
        // 3秒后自动隐藏错误信息
        setTimeout(() => {
            errorElement.style.display = 'none';
        }, 3000);
    }
    
    // 页面加载完成后初始化地图
    document.addEventListener('DOMContentLoaded', function() {
        initMap();
        
        // 绑定按钮事件
        document.getElementById('refresh-btn').addEventListener('click', loadDeviceLocations);
        document.getElementById('center-map-btn').addEventListener('click', centerMap);
        document.getElementById('toggle-cluster-btn').addEventListener('click', toggleMarkerCluster);
        document.getElementById('search-btn').addEventListener('click', searchDevice);
        
        // 添加回车键搜索支持
        document.getElementById('device-search').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchDevice();
            }
        });
        
        // 每5分钟自动刷新一次位置
        setInterval(loadDeviceLocations, 5 * 60 * 1000);
    });
</script>
{% endblock %}