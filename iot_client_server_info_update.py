from iot_client.platform.ali_mqtt_client import AmqpConfig
from iot_client.platform.emqx_mqtt_client import EMQXConfig
from iot_client.platform.platform_type import PlatformType
from iot_client.iot_client import IoTClient
from iot_client.bin_block.bin_block import BinBlock
from iot_client.functions.register_manager import Register<PERSON>anager
from iot_client.bin_block.bin_block import BinBlockMQ<PERSON><PERSON>rokerInfo_t
from iot_client.bin_block.protocol_constants import MqttBrokerType
import logging
import time


def config_alicloud_to_emqx(
    ori_devid, new_devid=None, ori_product_key="hs7eigK8Xvl", new_product_key="wxd48e69e833621cfd"
):
    new_devid = ori_devid

    # 构建topic
    topic_full_name = f"/{ori_product_key}/{ori_devid}/user/ota"
    # 创建寄存器管理器
    broker_info = BinBlockMQTTBrokerInfo_t(
        new_devid=new_devid,
        broker_type=MqttBrokerType.EMQX,
        port=1883,
        keep_alive=0,  # 不更新
        broker_host="mqtt01.yunpusher.com",
        username="kafang@",
        password="kafang@_2025",
        product_key=new_product_key,
        device_secret="",
    )
    reg_manager = RegisterManager(client, topic_full_name, logger)
    result = reg_manager.cmd_mqtt_broker_info_update(ori_devid, broker_info, restart=True, timeout=10)
    # result = reg_manager.read_registers(devid, 0, 1, timeout=10)
    print(result)
    if result.get("parsed_data", {}).get("result", None) == 0:
        print("更新成功")
    else:
        print("更新失败")


def config_emqx_to_alicloud(
    ori_devid,
    new_devid=None,
    new_device_secret="e50099fbc8ac924105ace0815d31b7a4",
    ori_product_key="wxd48e69e833621cfd",
    new_product_key="hs7eigK8Xvl",
):
    if new_devid is None:
        new_devid = ori_devid

    # 构建topic
    topic_full_name = f"/{ori_product_key}/{ori_devid}/user/ota"
    # 创建寄存器管理器
    broker_info = BinBlockMQTTBrokerInfo_t(
        new_devid=9999,
        broker_type=MqttBrokerType.ALIBABA_CLOUD,
        port=1883,
        keep_alive=0,  # 不更新，使用原来的
        broker_host="",  # 不更新,阿里云有product_key即可
        username="",  # 不更新，阿里云不需要
        password="",  # 不更新，阿里云不需要
        product_key=new_product_key,
        device_secret=new_device_secret,
    )
    reg_manager = RegisterManager(client, topic_full_name, logger)
    result = reg_manager.cmd_mqtt_broker_info_update(ori_devid, broker_info, restart=True, timeout=10)
    print(result)
    if result.get("parsed_data", {}).get("result", None) == 0:
        print("更新成功")
    else:
        print("更新失败")


def config_emqx_product_key(ori_devid, ori_product_key="wxd48e69e833621cfd", new_product_key="wx227137014f90cf30"):
    # 构建topic
    topic_full_name = f"/{ori_product_key}/{ori_devid}/user/ota"
    # 创建寄存器管理器
    broker_info = BinBlockMQTTBrokerInfo_t(
        new_devid=ori_devid,
        broker_type=MqttBrokerType.EMQX,
        port=1883,
        keep_alive=0,  # 不更新
        broker_host="mqtt01.yunpusher.com",
        username="kafang@",
        password="kafang@_2025",
        product_key=new_product_key,
        device_secret="",
    )
    reg_manager = RegisterManager(client, topic_full_name, logger)
    result = reg_manager.cmd_mqtt_broker_info_update(ori_devid, broker_info, restart=True, timeout=10)
    print(result)
    if result.get("parsed_data", {}).get("result", None) == 0:
        print("更新成功")
    else:
        print("更新失败")


def config_heart_and_billing_proto_type(devid, proto_type, product_key="wxd48e69e833621cfd"):
    topic_full_name = f"/{product_key}/{devid}/user/ota"
    reg_manager = RegisterManager(client, topic_full_name, logger)
    result = reg_manager.write_register(devid, 26+8, [8], timeout=10)
    result = reg_manager.read_registers(devid, 26+8, 1, timeout=10)
    print(result.get("parsed_data", {}))
    return
    curr_proto_type = result.get("parsed_data", {}).get("register_value", [-1])
    curr_proto_type = curr_proto_type[0]
    print(curr_proto_type)
    if curr_proto_type != proto_type:
        print(f"{devid}是旧版协议:{curr_proto_type}")
        result = reg_manager.write_register(devid, 34, [proto_type], timeout=10)
        print(curr_proto_type)
        result = reg_manager.read_registers(devid, 34, 1, timeout=10)
        # print(result)
        curr_proto_type = result.get("parsed_data", {}).get("register_value", [-1])
        if curr_proto_type == proto_type:
            print(f"{devid}更新成功")
    else:
        print(f"{devid}是最新协议")


# 示例用法
if __name__ == "__main__":
    # 关闭 stomp.py 的 DEBUG 日志
    logging.getLogger("stomp.py").setLevel(logging.INFO)

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(), logging.FileHandler("iot_client.log")],
    )
    logger = logging.getLogger("IoT_Client")

    # 创建配置
    config = AmqpConfig()
    exqx_config = EMQXConfig()
    # topic_filters = ["^/[^/]+/[^/]+/user/update$", "^/[^/]+/[^/]+/user/ota_ack$"]
    topic_filters = ["^/[^/]+/[^/]+/user/ota_ack$"]
    # 创建IoT客户端
    client = IoTClient(topic_filters, logger, config, exqx_config)

    # 启动客户端
    client.start()
    time.sleep(1)

    # config_alicloud_to_emqx(100001302, ori_product_key="hs7eigK8Xvl", new_product_key="wx227137014f90cf30")
    # config_alicloud_to_emqx(100001307, ori_product_key="hs7eigK8Xvl", new_product_key="wx227137014f90cf30")
    # config_alicloud_to_emqx(100001322, ori_product_key="hs7eigK8Xvl", new_product_key="wx227137014f90cf30")
    # config_alicloud_to_emqx(100001320, ori_product_key="hs7eigK8Xvl", new_product_key="wx227137014f90cf30")
    # config_alicloud_to_emqx(100001341, ori_product_key="hs7eigK8Xvl", new_product_key="wx227137014f90cf30")
    # config_alicloud_to_emqx(100001376, ori_product_key="hs7eigK8Xvl", new_product_key="wx227137014f90cf30")
    config_alicloud_to_emqx(100001397, ori_product_key="hs7eigK8Xvl", new_product_key="wx227137014f90cf30")

    exit()
    # config_alicloud_to_emqx(100001209, ori_product_key="hs7eigK8Xvl", new_product_key="wx227137014f90cf30")
    # config_emqx_product_key(100001209)
    # config_emqx_to_alicloud(9900)
    # config_heart_and_billing_proto_type(100001209, 5, product_key="wxd48e69e833621cfd")
    # config_heart_and_billing_proto_type(100001209, 5, product_key="wx227137014f90cf30")
    # config_heart_and_billing_proto_type(100001209, 5, product_key="hs7eigK8Xvl")
    # config_heart_and_billing_proto_type(100001063, 5)
    # config_heart_and_billing_proto_type(100001067, 5)
    # config_heart_and_billing_proto_type(100001220, 5)
    # config_heart_and_billing_proto_type(100001230, 5)
    device_id_s = [
        # 100001293,
        # 100001291,
        # 100001290,
        # 100001292,
        # 100001294,
        # 100001284,
        # 100001286,
        # 100001285,
        # 100001282,
        # 100001289,
        # 100001283,
        # 100001281,
        # 100001288,
        # 100001287,
        # 100001258,
        # 100001239,
        # 100001293,
        # 100001258,
        # 100001292,
        # 100001289,
        9998,
    ]
    for devid in device_id_s:
        config_heart_and_billing_proto_type(devid, 0, product_key="wx227137014f90cf30")
