import os
from datetime import timedelta
from pathlib import Path

class Config:
    """应用配置类"""
    # 基本配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key'
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or (
        # SQLite 默认路径 + 多线程优化参数
        f'sqlite:///{Path(__file__).parent}/instance/charging_pile.db'
        '?check_same_thread=False&timeout=20'  # 关键优化参数
    )
    # check_same_thread=False: 允许多线程共享连接池。
    # timeout=20: 设置锁等待超时时间为 20 秒。
    
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # 文件上传配置
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    FIRMWARE_UPLOAD_FOLDER = os.path.join(UPLOAD_FOLDER, 'firmware')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB

    # reCAPTCHA配置
    RECAPTCHA_PUBLIC_KEY = os.environ.get('RECAPTCHA_PUBLIC_KEY') or 'your-public-key'
    RECAPTCHA_SECRET_KEY = os.environ.get('RECAPTCHA_SECRET_KEY') or 'your-secret-key'

    # InfluxDB Edge 配置（嵌入式模式）
    INFLUXDB_DATA_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'influxdb')
    INFLUXDB_BUCKET = 'charging_pile_data'

    # 其他配置...
    SESSION_COOKIE_SECURE = False  # True 只通过 HTTPS 传输 cookie
    SESSION_COOKIE_HTTPONLY = True  # 防止 JavaScript 访问 cookie
    PERMANENT_SESSION_LIFETIME = timedelta(minutes=30)  # 会话超时时间
    SESSION_COOKIE_SAMESITE = 'Lax'  # 防止 CSRF 攻击
    # WTF_CSRF_ENABLED = False  # 禁用CSRF保护
