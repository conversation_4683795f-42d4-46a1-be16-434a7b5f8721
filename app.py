#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OTA设备管理后台
用于管理所有设备并进行OTA升级
"""

import sys
from utils.logger import LoggerManager
from utils.socket_manager import run_socketio
from app_factory import create_app, init_db

# 获取日志记录器
logger = LoggerManager.get_logger()

# 创建应用
app = create_app()

# 启动应用
if __name__ == "__main__":
    try:
        # 初始化数据库
        init_db(app)
        logger.info("数据库初始化完成")

        # 启动应用
        run_socketio(app, host="0.0.0.0", port=5001, debug=True)
    except Exception as e:
        logger.error(f"应用程序启动失败: {str(e)}")
        sys.exit(1)
