from datetime import datetime
from models.database import db

class OtaTask(db.Model):
    """OTA任务模型"""
    id = db.Column(db.Integer, primary_key=True)
    device_id = db.Column(db.Integer, db.<PERSON>ey('device.id'), nullable=False)
    firmware_path = db.Column(db.String(255), nullable=False)
    firmware_version = db.Column(db.String(20), nullable=False)
    status = db.Column(db.String(20), default="等待中")  # 等待中, 进行中, 成功, 失败
    progress = db.Column(db.Integer, default=0)  # 0-100
    error_message = db.Column(db.Text, default="")
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    device = db.relationship('Device', backref=db.backref('ota_task', lazy=True))