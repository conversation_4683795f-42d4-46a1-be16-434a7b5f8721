#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
AI功能路由
提供各种AI智能化功能的API接口
"""

from flask import Blueprint, render_template, request, jsonify, session, current_app
from flask_login import login_required, current_user
import json
from datetime import datetime
import logging

from services.ai_service import ai_service
from models.device import Device
from models.firmware import Firmware
from models.ota_task import OtaTask
from models.database import db

# 获取日志记录器
logger = logging.getLogger(__name__)

# 创建蓝图
ai_bp = Blueprint('ai', __name__, url_prefix='/ai')

@ai_bp.route('/dashboard')
@login_required
def ai_dashboard():
    """AI智能分析仪表板页面"""
    return render_template('ai/dashboard.html')

@ai_bp.route('/device_analysis/<int:device_id>')
@login_required
def device_analysis(device_id):
    """设备智能分析页面"""
    device = Device.query.get_or_404(device_id)
    
    # 获取设备位置信息
    from models.device_location import DeviceLocation
    location = DeviceLocation.query.filter_by(device_id=device_id).first()
    location_info = location.location if location else "未知"
    
    # 构建设备数据
    device_data = {
        'device_id': device.device_id,
        'firmware_version': device.firmware_version,
        'location': location_info
    }
    
    # 获取设备历史任务
    tasks = OtaTask.query.filter_by(device_id=device_id).order_by(OtaTask.created_at.desc()).limit(10).all()
    task_history = []
    for task in tasks:
        task_history.append({
            'timestamp': task.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'status': task.status,
            'event': f"OTA升级任务，固件版本: {task.firmware_version}"
        })
    
    # 获取可用固件
    firmwares = Firmware.query.order_by(Firmware.upload_time.desc()).all()
    available_firmwares = []
    for firmware in firmwares:
        available_firmwares.append({
            'version': firmware.version,
            'release_date': firmware.upload_time.strftime('%Y-%m-%d'),
            'description': firmware.description
        })
    
    return render_template('ai/device_analysis.html', 
                          device=device, 
                          device_data=device_data,
                          task_history=task_history,
                          available_firmwares=available_firmwares)

@ai_bp.route('/api/analyze_device', methods=['POST'])
@login_required
def analyze_device():
    """分析设备数据API"""
    try:
        data = request.get_json()
        if not data or 'device_id' not in data:
            return jsonify({'success': False, 'message': '缺少设备ID'})
        
        device_id = data['device_id']
        device = Device.query.get_or_404(device_id)
        
        # 获取设备位置信息
        from models.device_location import DeviceLocation
        location = DeviceLocation.query.filter_by(device_id=device_id).first()
        location_info = location.location if location else "未知"
        
        # 构建设备数据
        device_data = {
            'device_id': device.device_id,
            'firmware_version': device.firmware_version,
            'last_online_time': device.last_online_time.strftime('%Y-%m-%d %H:%M:%S') if device.last_online_time else '未知',
            'location': location_info
        }
        
        # 调用AI服务分析设备数据
        result = ai_service.analyze_device_data(device_data)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"分析设备数据失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@ai_bp.route('/api/predict_failure', methods=['POST'])
@login_required
def predict_failure():
    """预测设备故障API"""
    try:
        data = request.get_json()
        if not data or 'device_id' not in data:
            return jsonify({'success': False, 'message': '缺少设备ID'})
        
        device_id = data['device_id']
        
        # 获取设备历史任务
        tasks = OtaTask.query.filter_by(device_id=device_id).order_by(OtaTask.created_at.desc()).limit(20).all()
        task_history = []
        for task in tasks:
            task_history.append({
                'timestamp': task.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'status': task.status,
                'event': f"OTA升级任务，固件版本: {task.firmware_version}"
            })
        
        # 调用AI服务预测故障
        result = ai_service.predict_device_failure(task_history)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"预测设备故障失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@ai_bp.route('/api/recommend_upgrade', methods=['POST'])
@login_required
def recommend_upgrade():
    """推荐固件升级API"""
    try:
        data = request.get_json()
        if not data or 'device_id' not in data:
            return jsonify({'success': False, 'message': '缺少设备ID'})
        
        device_id = data['device_id']
        device = Device.query.get_or_404(device_id)
        
        # 构建设备信息
        device_info = {
            'device_id': device.device_id,
            'firmware_version': device.firmware_version,
            'device_type': device.device_type,
            'last_upgrade_time': device.last_ota_time.strftime('%Y-%m-%d %H:%M:%S') if device.last_ota_time else '未知'
        }
        
        # 获取可用固件
        firmwares = Firmware.query.order_by(Firmware.upload_time.desc()).all()
        available_firmwares = []
        for firmware in firmwares:
            available_firmwares.append({
                'version': firmware.version,
                'release_date': firmware.upload_time.strftime('%Y-%m-%d'),
                'description': firmware.description
            })
        
        # 调用AI服务推荐升级
        result = ai_service.recommend_firmware_upgrade(device_info, available_firmwares)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"推荐固件升级失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@ai_bp.route('/chat')
@login_required
def chat():
    """AI助手聊天页面"""
    return render_template('ai/chat.html')

@ai_bp.route('/api/chat', methods=['POST'])
@login_required
def chat_api():
    """AI助手聊天API"""
    try:
        data = request.get_json()
        message = data.get('message', '')
        
        if not message:
            return jsonify({'success': False, 'message': '消息不能为空'})
        
        # 获取对话历史
        conversation_history = session.get('chat_history', [])
        
        # 生成AI响应
        response = ai_service.generate_chat_response(message, conversation_history)
        
        # 更新对话历史
        conversation_history.append({'role': 'user', 'content': message})
        conversation_history.append({'role': 'assistant', 'content': response})
        session['chat_history'] = conversation_history
        
        return jsonify({
            'success': True,
            'response': response
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@ai_bp.route('/api/clear_history', methods=['POST'])
@login_required
def clear_chat_history():
    """清除聊天历史"""
    try:
        session.pop('chat_history', None)
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@ai_bp.route('/api/system_status', methods=['GET'])
def get_system_status():
    """获取系统状态"""
    try:
        status = ai_service.get_device_status_summary()
        return jsonify({
            'success': True,
            'data': status
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}) 